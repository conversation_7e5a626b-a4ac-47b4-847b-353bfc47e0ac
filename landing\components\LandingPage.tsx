"use client"

import React from 'react'
import { motion } from 'framer-motion'
import { LandingNavigation } from './LandingNavigation'
import { Safe3DComponent } from './ErrorBoundary'
import { FeaturesShowcaseSection } from './sections/FeaturesShowcaseSection'
import { CommunityHighlightsSection } from './sections/CommunityHighlightsSection'
import { TestimonialsSection } from './sections/TestimonialsSection'
import { CTASection } from './sections/CTASection'
// Lazy load heavy 3D components for better performance
const QuantumArrivalHero = React.lazy(() => import('./sections/QuantumArrivalHero').then(m => ({ default: m.QuantumArrivalHero })))
const DeCodeLearningSection = React.lazy(() => import('./sections/DeCodeLearningSection').then(m => ({ default: m.DeCodeLearningSection })))
const GameLikeJourneyTrail = React.lazy(() => import('./sections/GameLikeJourneyTrail').then(m => ({ default: m.GameLikeJourneyTrail })))
const DigitalTrustSection = React.lazy(() => import('./sections/DigitalTrustSection').then(m => ({ default: m.DigitalTrustSection })))
const InteractiveByteverseMap = React.lazy(() => import('./sections/InteractiveByteverseMap').then(m => ({ default: m.InteractiveByteverseMap })))
const CallToAdventureQuest = React.lazy(() => import('./sections/CallToAdventureQuest').then(m => ({ default: m.CallToAdventureQuest })))
const AISidekickByteSection = React.lazy(() => import('./sections/AISidekickByteSection').then(m => ({ default: m.AISidekickByteSection })))
const JourneyTimelineSection = React.lazy(() => import('./sections/JourneyTimelineSection').then(m => ({ default: m.JourneyTimelineSection })))
const SafetyEducationSection = React.lazy(() => import('./sections/SafetyEducationSection').then(m => ({ default: m.SafetyEducationSection })))
const FinalCTASection = React.lazy(() => import('./sections/FinalCTASection').then(m => ({ default: m.FinalCTASection })))
const FloatingCTA = React.lazy(() => import('./sections/FloatingCTA').then(m => ({ default: m.FloatingCTA })))
const ByteMentorChat = React.lazy(() => import('./sections/ByteMentorChat').then(m => ({ default: m.ByteMentorChat })))
import { Footer } from './sections/Footer'

import { SignupModal } from './SignupModal'
import { Dialog } from '@/components/ui/dialog'
import { useLandingPageState } from '../hooks/useLandingPageState'

interface LandingPageProps {
  onLogin: () => void
}

export function LandingPage({ onLogin }: LandingPageProps) {
  const {
    currentTestimonial,
    setCurrentTestimonial,
    showSignupModal,
    setShowSignupModal,
    scrollProgress,
    showFloatingCTA: _showFloatingCTA,
    signupData,
    setSignupData,
    showChatAssistant: _showChatAssistant,
    setShowChatAssistant: _setShowChatAssistant,
    chatMessages: _chatMessages,
    chatInput: _chatInput,
    setChatInput: _setChatInput,
    emailReminder: _emailReminder,
    setEmailReminder: _setEmailReminder,
    byteMentorMessage: _byteMentorMessage,
    showByteMentor: _showByteMentor,
    setShowByteMentor: _setShowByteMentor,
    handleSignup,
    toggleInterest,
    handleChatSubmit: _handleChatSubmit,
    handleEmailReminder: _handleEmailReminder,
  } = useLandingPageState(onLogin)

  return (
    <div className="w-full min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-blue-900 text-white overflow-x-hidden">
      {/* Progress indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 to-yellow-500 z-50 origin-left"
        style={{ scaleX: scrollProgress }}
      />

      <div className="relative z-10">
        {/* Navigation - constrained width */}
        <div className="w-full">
          <LandingNavigation onLogin={onLogin} />
        </div>

        {/* Quantum Arrival Hero Section - full width */}
        <div className="w-full">
          <Safe3DComponent>
            <React.Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
              <QuantumArrivalHero onScrollToNext={() => {
                const element = document.querySelector('#decode')
                if (element) {
                  element.scrollIntoView({ behavior: 'smooth' })
                }
              }} />
            </React.Suspense>
          </Safe3DComponent>
        </div>

        {/* DeCode Learning Section */}
        {/* <section id="decode">
          <Safe3DComponent>
            <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
              <DeCodeLearningSection onSignupClick={() => setShowSignupModal(true)} />
            </React.Suspense>
          </Safe3DComponent>
        </section> */}

        {/* Game-Like Journey Trail */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <GameLikeJourneyTrail onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Journey Timeline Section */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <JourneyTimelineSection onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Features Showcase */}
        {/* <section id="features">
          <FeaturesShowcaseSection onSignupClick={() => setShowSignupModal(true)} />
        </section> */}

        {/* Digital Trust Section */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <DigitalTrustSection />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Safety Education Section */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <SafetyEducationSection />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Interactive Byteverse Map */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <InteractiveByteverseMap onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Community Highlights */}
        {/* <CommunityHighlightsSection /> */}

        {/* Call to Adventure Quest */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <CallToAdventureQuest onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* AI Sidekick Byte Section */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <AISidekickByteSection onSignupClick={() => setShowSignupModal(true)} />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Testimonials */}
        {/* <TestimonialsSection
          currentTestimonial={currentTestimonial}
          setCurrentTestimonial={setCurrentTestimonial}
        /> */}

        {/* CTA Section */}
        {/* <CTASection onSignupClick={() => setShowSignupModal(true)} /> */}

        {/* Final CTA Section */}
        {/* <Safe3DComponent>
          <React.Suspense fallback={<div className="h-96 flex items-center justify-center">Loading...</div>}>
            <FinalCTASection
              showSignupModal={showSignupModal}
              setShowSignupModal={setShowSignupModal}
            />
          </React.Suspense>
        </Safe3DComponent> */}

        {/* Footer - full width */}
        <Footer />
       

        
      </div>

      {/* Signup Modal */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <SignupModal
          isOpen={showSignupModal}
          onClose={() => setShowSignupModal(false)}
          signupData={signupData}
          setSignupData={setSignupData}
          onSignup={handleSignup}
          onToggleInterest={toggleInterest}
        />
      </Dialog>
    </div>
  )
}
